import { <PERSON><PERSON><PERSON>, PrimaryGeneratedColumn, Column, OneToMany, CreateDateColumn, UpdateDateColumn } from 'typeorm';
import { Opportunity } from './opportunity.entity';
import { Contact } from './contact.entity';
import { Task } from './task.entity';

@Entity('users')
export class User {
    @PrimaryGeneratedColumn('uuid')
    id: string;

    @Column()
    username: string;

    @Column()
    name: string;

    @Column({ default: 'active', type: 'enum', enum: ['active', 'inactive'] })
    status: 'active' | 'inactive';

    @OneToMany(() => Task, (task: Task) => task.assignedTo)
    tasks: Task[];

    @OneToMany(() => Opportunity, opportunity => opportunity.owner)
    opportunities: Opportunity[];

    @OneToMany(() => Contact, contact => contact.assignedTo)
    contacts: Contact[];
}
