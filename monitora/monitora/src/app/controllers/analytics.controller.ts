import { Controller, Get, Middleware } from '@core/decorators/decorators';
import { Request, Response } from 'express';
import { auth } from '../middlewares/auth.middleware';
import { AnalyticsService } from '../services/analytics.service';

@Controller('/analytics')
export class AnalyticsController {
  constructor(private analyticsService: AnalyticsService) {}

  @Get('/metrics')
  @Middleware(auth)
  async getMetrics(req: Request, res: Response) {
    const metrics = await this.analyticsService.getMetrics({
      startDate: req.query.startDate as string,
      endDate: req.query.endDate as string,
      metrics: (req.query.metrics as string).split(',')
    });
    res.json(metrics);
  }

  @Get('/trends')
  @Middleware(auth)
  async getTrends(req: Request, res: Response) {
    const trends = await this.analyticsService.getTrends({
      metric: req.query.metric as string,
      period: req.query.period as string
    });
    res.json(trends);
  }
}