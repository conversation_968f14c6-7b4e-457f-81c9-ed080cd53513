import { Controller, Get, Post, Put, Delete, Middleware } from '@core/decorators/decorators';
import { Request, Response } from 'express';
import { DomainMonitoringService } from '../services/domain-monitoring.service';
import { SocialMediaMonitoringService } from '../services/social-media-monitoring.service';
import { BrandService } from '../services/brand.service';
import { auth } from '../middlewares/auth.middleware';

@Controller('/monitoring')
export class MonitoringController {
  constructor(
    private domainMonitoringService: DomainMonitoringService,
    private socialMediaMonitoringService: SocialMediaMonitoringService,
    private brandService: BrandService
  ) {}

  @Post('/scan/domains/:brandId')
  @Middleware(auth)
  async scanDomains(req: Request, res: Response) {
    try {
      const brand = await this.brandService.findById(req.params.brandId);
      if (!brand) {
        return res.status(404).json({ message: 'Brand not found' });
      }

      // Check ownership
      if (brand.ownerId !== req.currentUser.id) {
        return res.status(403).json({ message: 'Access denied' });
      }

      const suspiciousDomains = await this.domainMonitoringService.scanForSimilarDomains(brand);
      res.json({
        message: 'Domain scan completed',
        found: suspiciousDomains.length,
        domains: suspiciousDomains
      });
    } catch (error) {
      res.status(500).json({ message: 'Error scanning domains', error: error.message });
    }
  }

  @Post('/scan/social-media/:brandId')
  @Middleware(auth)
  async scanSocialMedia(req: Request, res: Response) {
    try {
      const brand = await this.brandService.findById(req.params.brandId);
      if (!brand) {
        return res.status(404).json({ message: 'Brand not found' });
      }

      // Check ownership
      if (brand.ownerId !== req.currentUser.id) {
        return res.status(403).json({ message: 'Access denied' });
      }

      const { platforms } = req.body;
      const suspiciousContent = await this.socialMediaMonitoringService.scanSocialMedia(brand, platforms);
      
      res.json({
        message: 'Social media scan completed',
        found: suspiciousContent.length,
        content: suspiciousContent
      });
    } catch (error) {
      res.status(500).json({ message: 'Error scanning social media', error: error.message });
    }
  }

  @Get('/domains/:brandId')
  @Middleware(auth)
  async getDomainMonitors(req: Request, res: Response) {
    try {
      const brand = await this.brandService.findById(req.params.brandId);
      if (!brand) {
        return res.status(404).json({ message: 'Brand not found' });
      }

      // Check ownership
      if (brand.ownerId !== req.currentUser.id) {
        return res.status(403).json({ message: 'Access denied' });
      }

      const domains = await this.domainMonitoringService.getDomainMonitors(req.params.brandId);
      res.json(domains);
    } catch (error) {
      res.status(500).json({ message: 'Error fetching domain monitors', error: error.message });
    }
  }

  @Get('/social-media/:brandId')
  @Middleware(auth)
  async getSocialMediaMonitors(req: Request, res: Response) {
    try {
      const brand = await this.brandService.findById(req.params.brandId);
      if (!brand) {
        return res.status(404).json({ message: 'Brand not found' });
      }

      // Check ownership
      if (brand.ownerId !== req.currentUser.id) {
        return res.status(403).json({ message: 'Access denied' });
      }

      const socialMedia = await this.socialMediaMonitoringService.getSocialMediaMonitors(req.params.brandId);
      res.json(socialMedia);
    } catch (error) {
      res.status(500).json({ message: 'Error fetching social media monitors', error: error.message });
    }
  }

  @Put('/domains/:id/status')
  @Middleware(auth)
  async updateDomainStatus(req: Request, res: Response) {
    try {
      const { status, notes } = req.body;
      
      if (!status) {
        return res.status(400).json({ message: 'Status is required' });
      }

      const updatedDomain = await this.domainMonitoringService.updateDomainStatus(
        req.params.id, 
        status, 
        notes
      );

      if (!updatedDomain) {
        return res.status(404).json({ message: 'Domain monitor not found' });
      }

      res.json(updatedDomain);
    } catch (error) {
      res.status(500).json({ message: 'Error updating domain status', error: error.message });
    }
  }

  @Put('/social-media/:id/status')
  @Middleware(auth)
  async updateSocialMediaStatus(req: Request, res: Response) {
    try {
      const { status, notes } = req.body;
      
      if (!status) {
        return res.status(400).json({ message: 'Status is required' });
      }

      const updatedSocialMedia = await this.socialMediaMonitoringService.updateSocialMediaStatus(
        req.params.id, 
        status, 
        notes
      );

      if (!updatedSocialMedia) {
        return res.status(404).json({ message: 'Social media monitor not found' });
      }

      res.json(updatedSocialMedia);
    } catch (error) {
      res.status(500).json({ message: 'Error updating social media status', error: error.message });
    }
  }

  @Post('/domains/:id/recheck')
  @Middleware(auth)
  async recheckDomain(req: Request, res: Response) {
    try {
      const updatedDomain = await this.domainMonitoringService.recheckDomain(req.params.id);
      
      if (!updatedDomain) {
        return res.status(404).json({ message: 'Domain monitor not found' });
      }

      res.json(updatedDomain);
    } catch (error) {
      res.status(500).json({ message: 'Error rechecking domain', error: error.message });
    }
  }

  @Get('/dashboard/:brandId')
  @Middleware(auth)
  async getDashboard(req: Request, res: Response) {
    try {
      const brand = await this.brandService.findById(req.params.brandId);
      if (!brand) {
        return res.status(404).json({ message: 'Brand not found' });
      }

      // Check ownership
      if (brand.ownerId !== req.currentUser.id) {
        return res.status(403).json({ message: 'Access denied' });
      }

      const [
        domainStats,
        socialMediaStats,
        brandStats
      ] = await Promise.all([
        this.domainMonitoringService.getDomainStats(req.params.brandId),
        this.socialMediaMonitoringService.getSocialMediaStats(req.params.brandId),
        this.brandService.getBrandStats(req.params.brandId)
      ]);

      res.json({
        brand: {
          id: brand.id,
          name: brand.name,
          status: brand.status
        },
        stats: brandStats,
        domains: domainStats,
        socialMedia: socialMediaStats
      });
    } catch (error) {
      res.status(500).json({ message: 'Error fetching dashboard data', error: error.message });
    }
  }
}
