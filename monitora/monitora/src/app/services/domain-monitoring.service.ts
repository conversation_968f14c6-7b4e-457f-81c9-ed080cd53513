import { Provider } from '@core/decorators/decorators';
import { DomainMonitorRepository } from '../repositories/domain-monitor.repository';
import { ThreatDetectionService } from './threat-detection.service';
import { WhoisGateway } from '../gateways/whois.gateway';
import { DomainScannerGateway } from '../gateways/domain-scanner.gateway';
import { Mock } from '@core/decorators/mock.decorator';
import { DomainMonitor } from '../../entities/domain-monitor.entity';
import { Brand } from '../../entities/brand.entity';
import * as similarity from 'similarity';

@Provider()
export class DomainMonitoringService {
  constructor(
    private domainMonitorRepository: DomainMonitorRepository,
    private threatDetectionService: ThreatDetectionService,
    private whoisGateway: WhoisGateway,
    private domainScannerGateway: DomainScannerGateway
  ) {}

  @Mock('DomainMonitoringService-scanForSimilarDomains')
  async scanForSimilarDomains(brand: Brand): Promise<DomainMonitor[]> {
    const suspiciousDomains: DomainMonitor[] = [];
    
    if (!brand.primaryDomain || !brand.keywords) {
      return suspiciousDomains;
    }

    // Generate potential typosquatting domains
    const potentialDomains = this.generatePotentialDomains(brand.primaryDomain, brand.keywords);
    
    for (const domain of potentialDomains) {
      try {
        // Check if domain is registered
        const whoisData = await this.whoisGateway.lookup(domain);
        
        if (whoisData && whoisData.registrationDate) {
          // Calculate similarity score
          const similarityScore = similarity(brand.primaryDomain, domain);
          
          // Check if domain already exists in monitoring
          const existingMonitor = await this.domainMonitorRepository.findByDomain(domain);
          
          if (!existingMonitor && similarityScore > 0.7) {
            // Scan website content
            const websiteData = await this.domainScannerGateway.scanWebsite(domain);
            
            // Determine detection type
            const detectionType = this.determineDetectionType(domain, brand.primaryDomain);
            
            // Assess risk factors
            const riskFactors = this.assessRiskFactors(domain, websiteData, brand);
            
            const domainMonitor = await this.domainMonitorRepository.create({
              domain,
              brandId: brand.id,
              status: 'suspicious',
              similarityScore,
              whoisData,
              websiteData,
              riskFactors,
              detectionType,
              firstDetected: new Date(),
              lastChecked: new Date(),
              isActive: true
            });

            suspiciousDomains.push(domainMonitor);

            // Create threat detection if high risk
            if (this.isHighRisk(riskFactors)) {
              await this.threatDetectionService.createFromDomainMonitor(domainMonitor);
            }
          }
        }
      } catch (error) {
        console.error(`Error scanning domain ${domain}:`, error);
      }
    }

    return suspiciousDomains;
  }

  @Mock('DomainMonitoringService-updateDomainStatus')
  async updateDomainStatus(id: string, status: DomainMonitor['status'], notes?: string): Promise<DomainMonitor | null> {
    const updateData: Partial<DomainMonitor> = { status, lastChecked: new Date() };
    if (notes) {
      updateData.notes = notes;
    }
    return this.domainMonitorRepository.update(id, updateData);
  }

  @Mock('DomainMonitoringService-recheckDomain')
  async recheckDomain(id: string): Promise<DomainMonitor | null> {
    const monitor = await this.domainMonitorRepository.findById(id);
    if (!monitor) return null;

    try {
      // Update WHOIS data
      const whoisData = await this.whoisGateway.lookup(monitor.domain);
      
      // Update website data
      const websiteData = await this.domainScannerGateway.scanWebsite(monitor.domain);
      
      // Reassess risk factors
      const brand = await this.domainMonitorRepository.getBrandForDomain(id);
      const riskFactors = brand ? this.assessRiskFactors(monitor.domain, websiteData, brand) : monitor.riskFactors;

      return this.domainMonitorRepository.update(id, {
        whoisData,
        websiteData,
        riskFactors,
        lastChecked: new Date()
      });
    } catch (error) {
      console.error(`Error rechecking domain ${monitor.domain}:`, error);
      return this.domainMonitorRepository.update(id, { lastChecked: new Date() });
    }
  }

  private generatePotentialDomains(primaryDomain: string, keywords: string[]): string[] {
    const domains: string[] = [];
    const [domainName, tld] = primaryDomain.split('.');
    
    // Typosquatting variations
    domains.push(...this.generateTyposquattingDomains(domainName, tld));
    
    // Keyword stuffing
    keywords.forEach(keyword => {
      domains.push(`${keyword}${domainName}.${tld}`);
      domains.push(`${domainName}${keyword}.${tld}`);
      domains.push(`${keyword}-${domainName}.${tld}`);
      domains.push(`${domainName}-${keyword}.${tld}`);
    });
    
    // TLD variations
    const commonTlds = ['com', 'net', 'org', 'info', 'biz', 'co', 'io'];
    commonTlds.forEach(newTld => {
      if (newTld !== tld) {
        domains.push(`${domainName}.${newTld}`);
      }
    });

    return [...new Set(domains)]; // Remove duplicates
  }

  private generateTyposquattingDomains(domainName: string, tld: string): string[] {
    const domains: string[] = [];
    
    // Character substitution
    const substitutions: { [key: string]: string[] } = {
      'a': ['e', 'o'],
      'e': ['a', 'i'],
      'i': ['e', 'o'],
      'o': ['a', 'i'],
      'u': ['o', 'i'],
      'm': ['n'],
      'n': ['m'],
      'l': ['i', '1'],
      '1': ['l', 'i']
    };

    for (let i = 0; i < domainName.length; i++) {
      const char = domainName[i];
      if (substitutions[char]) {
        substitutions[char].forEach(sub => {
          const typo = domainName.substring(0, i) + sub + domainName.substring(i + 1);
          domains.push(`${typo}.${tld}`);
        });
      }
    }

    // Character omission
    for (let i = 0; i < domainName.length; i++) {
      const omission = domainName.substring(0, i) + domainName.substring(i + 1);
      if (omission.length > 2) {
        domains.push(`${omission}.${tld}`);
      }
    }

    // Character insertion
    const chars = 'abcdefghijklmnopqrstuvwxyz';
    for (let i = 0; i <= domainName.length; i++) {
      for (const char of chars) {
        const insertion = domainName.substring(0, i) + char + domainName.substring(i);
        domains.push(`${insertion}.${tld}`);
      }
    }

    return domains;
  }

  private determineDetectionType(domain: string, primaryDomain: string): DomainMonitor['detectionType'] {
    const [domainName] = domain.split('.');
    const [primaryName] = primaryDomain.split('.');

    if (domain.includes(primaryName) && domain !== primaryDomain) {
      return 'subdomain';
    }

    if (domain.split('.')[1] !== primaryDomain.split('.')[1]) {
      return 'tld_variation';
    }

    if (this.isTyposquatting(domainName, primaryName)) {
      return 'typosquatting';
    }

    if (this.isHomograph(domainName, primaryName)) {
      return 'homograph';
    }

    return 'keyword_stuffing';
  }

  private isTyposquatting(domain: string, primary: string): boolean {
    const distance = this.levenshteinDistance(domain, primary);
    return distance <= 2 && distance > 0;
  }

  private isHomograph(domain: string, primary: string): boolean {
    // Simple homograph detection - in real implementation, use Unicode confusables
    const homographs: { [key: string]: string[] } = {
      'a': ['а', 'α'],
      'e': ['е', 'ε'],
      'o': ['о', 'ο'],
      'p': ['р', 'ρ'],
      'c': ['с', 'ϲ']
    };

    for (const [latin, alternatives] of Object.entries(homographs)) {
      if (primary.includes(latin)) {
        for (const alt of alternatives) {
          if (domain.includes(alt)) {
            return true;
          }
        }
      }
    }

    return false;
  }

  private levenshteinDistance(str1: string, str2: string): number {
    const matrix = Array(str2.length + 1).fill(null).map(() => Array(str1.length + 1).fill(null));

    for (let i = 0; i <= str1.length; i++) matrix[0][i] = i;
    for (let j = 0; j <= str2.length; j++) matrix[j][0] = j;

    for (let j = 1; j <= str2.length; j++) {
      for (let i = 1; i <= str1.length; i++) {
        const indicator = str1[i - 1] === str2[j - 1] ? 0 : 1;
        matrix[j][i] = Math.min(
          matrix[j][i - 1] + 1,
          matrix[j - 1][i] + 1,
          matrix[j - 1][i - 1] + indicator
        );
      }
    }

    return matrix[str2.length][str1.length];
  }

  private assessRiskFactors(domain: string, websiteData: any, brand: Brand): any[] {
    const riskFactors = [];

    // Check for brand keywords in content
    if (websiteData?.content && brand.keywords) {
      const contentLower = websiteData.content.toLowerCase();
      const matchingKeywords = brand.keywords.filter(keyword => 
        contentLower.includes(keyword.toLowerCase())
      );

      if (matchingKeywords.length > 0) {
        riskFactors.push({
          factor: 'Brand keywords in content',
          severity: 'high',
          description: `Found brand keywords: ${matchingKeywords.join(', ')}`
        });
      }
    }

    // Check for suspicious SSL certificate
    if (websiteData?.ssl && !websiteData.ssl.valid) {
      riskFactors.push({
        factor: 'Invalid SSL certificate',
        severity: 'medium',
        description: 'Website has invalid or expired SSL certificate'
      });
    }

    // Check for recent registration
    if (websiteData?.whoisData?.registrationDate) {
      const regDate = new Date(websiteData.whoisData.registrationDate);
      const daysSinceReg = (Date.now() - regDate.getTime()) / (1000 * 60 * 60 * 24);
      
      if (daysSinceReg < 30) {
        riskFactors.push({
          factor: 'Recently registered domain',
          severity: 'high',
          description: `Domain registered ${Math.floor(daysSinceReg)} days ago`
        });
      }
    }

    return riskFactors;
  }

  private isHighRisk(riskFactors: any[]): boolean {
    return riskFactors.some(factor => factor.severity === 'critical' || factor.severity === 'high');
  }
}
